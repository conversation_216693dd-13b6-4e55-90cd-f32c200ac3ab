<template>
  <div>
    <!-- v-show="!props.markTabChange" -->
    <div v-if="!props.markTabChange" class="flex-bc">
      <el-page-header @back="jumpTo('vulnerabilityHome')" class="mb-2">
        <template #content>
          <span class="mr-3 font-bold">
            漏洞详情 ({{ props.eventInfo["ip"] }})
          </span>
        </template>
      </el-page-header>
      <div style="font-size: 14px" class="mb-1 mr-3">
        切换资产信息：<el-select
          v-model="value1"
          multiple
          placeholder="资产信息"
          style="width: 240px"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
    </div>
    <div style="margin-top: 2px" class="ml-2 mr-2">
      <im-table
        class="vul_table_view_vul"
        ref="tableRef"
        showOverflowTooltip
        :data="tableData"
        center
        toolbar
        :table-alert="{
          closable: false
        }"
        :operator="{
          label: '操作',
          width: 160
        }"
        :height="tableOption.height"
        :stripe="tableOption.stripe"
        show-checkbox
        :column-storage="createColumnStorage('vul-vul', 'remote')"
        :pagination="tablePage"
        :loading="tableLoading"
        :filter-data-provider="filterDataProvider"
        @on-reload="resetTablePageAndQuery"
        @selection-change="selectionChangeHandler"
        @on-page-change="queryEventData"
      >
        <!-- 表格左侧菜单 -->
        <template #toolbar-left="{ size }">
          <div class="flex-sc pt-0.5">
            <!-- 分段选择器，用于选择事件处理状态 -->
            <el-segmented
              v-model="searchCondition['dealWith']"
              :options="segmentData"
              @change="segmentChangeHandler"
            ></el-segmented>
          </div>
        </template>

        <!-- 表格右侧菜单 -->
        <template #toolbar-right="{ size }">
          <div class="float-left flex-sc pr-3 pt-0.5 gap-3">
            <el-segmented
              v-model="state.dateRangeSign"
              :options="state.timeSegmentOptions"
              @change="
                () => {
                  dateTimeRange = [];
                  resetTablePageAndQuery();
                }
              "
            >
            </el-segmented>
            <!-- 日期选择器，用于选择事件时间范围 -->
            <el-date-picker
              clearable
              v-model="dateTimeRange"
              type="daterange"
              range-separator="到"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 200px"
              @change="dateChangeFunction"
            />

            <!-- 数据来源选择器 -->
            <el-select
              v-model="searchCondition['syscode']"
              placeholder="数据来源"
              clearable
              collapse-tags
              multiple
              style="width: 160px"
              @change="syscodeChangeHandler"
            >
              <el-option
                v-for="item in state.syscodeData"
                :key="item"
                :label="item"
                :value="item"
              />
            </el-select>

            <!-- 风险级别选择器，用于选择事件风险级别 -->
            <el-select
              v-model="searchCondition['vulLevel']"
              placeholder="漏洞级别"
              multiple
              collapse-tags
              clearable
              style="width: 150px"
              @change="riskLevelChangeHandler"
            >
              <el-option
                v-for="item in riskLevelData02"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              />
            </el-select>

            <!-- vulName 漏洞名称 -->
            <el-input
              v-if="!props.markTabChange"
              clearable
              @change="queryEventData"
              v-model="vulName"
              style="width: 150px"
              size="small"
              placeholder="漏洞名称"
              :suffix-icon="Search"
            />

            <!-- 更多操作 -->
            <el-dropdown style="margin: 0 10px">
              <el-button type="primary"> 批量操作 </el-button>
              <template #dropdown>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item
                    :disabled="
                      state.selectedEventRows.length == 0 || !canBatchDispose
                    "
                    style="padding: 0.4rem 1rem"
                    @click.native="handleCommand('批量处置', 'moreFunction')"
                  >
                    批量处置
                  </el-dropdown-item>
                  <el-dropdown-item
                    :disabled="
                      state.selectedEventRows.length == 0 || !canBatchRemove
                    "
                    style="padding: 0.4rem 1rem"
                    @click.native="handleCommand('批量移除', '批量移除')"
                  >
                    批量移除
                  </el-dropdown-item>
                  <!-- <el-dropdown-item :disabled="(state.selectedEventRows.length == 0)"
                                        style="padding: 0.4rem 1rem;" v-for="(item, index) in batchOperation"
                                        :key="index"
                                        @click.native="item == '批量派单' ? handleCommand(item, '批量派单') : handleCommand(item, 'moreFunction')">{{
                                            item }}
                                    </el-dropdown-item> -->
                  <!-- <el-dropdown-item style="padding: 0.4rem 1rem;"
                                        :disabled="(state.selectedEventRows.length == 0)"
                                        @click.native="openEditTaskDialog(({ operationVulDataList: state.selectedEvents }))">
                                        批量验证
                                    </el-dropdown-item> -->
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 导入 -->
            <el-dropdown @command="handleCommand" placement="bottom">
              <el-button
                >导入<svg
                  style="
                    width: 17px;
                    height: 16px;
                    margin-bottom: 2px;
                    margin-left: 2px;
                    color: #666;
                  "
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 1024 1024"
                >
                  <path
                    fill="currentColor"
                    d="M544 864V672h128L512 480 352 672h128v192H320v-1.6c-5.376.32-10.496 1.6-16 1.6A240 240 0 0 1 64 624c0-123.136 93.12-223.488 212.608-237.248A239.808 239.808 0 0 1 512 192a239.872 239.872 0 0 1 235.456 194.752c119.488 13.76 212.48 114.112 212.48 237.248a240 240 0 0 1-240 240c-5.376 0-10.56-1.28-16-1.6v1.6z"
                  ></path>
                </svg>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    style="padding: 0.4rem 1rem"
                    v-for="item in importTemplate"
                    :key="item['type']"
                    :command="item['type']"
                    >{{ item["name"] }}</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>

            <!-- 导出事件数据 -->
            <el-tooltip content="导出数据" placement="top" :open-delay="1000">
              <el-button
                :icon="useRenderIcon('EP-Download')"
                circle
                :size="size"
                :disabled="tableData.length == 0"
                @click="exportEventHandler"
              />
            </el-tooltip>
          </div>
        </template>
        <!-- 表格操作按钮 -->
        <template #operator="{ row, size, type }">
          <!-- 处置按钮 - 根据页签和状态控制显示 -->
          <el-button
            v-if="canShowDisposeButton(row)"
            :size="size"
            :type="type"
            text
            :icon="useRenderIcon('EP-Checked')"
            @click="handleCommand(row, 'moreFunction', 'one')"
          >
            处置
          </el-button>
          <!-- 移除按钮 - 仅对处置状态为"无需处理"的行显示 -->
          <el-popconfirm
            v-if="row['dealStatus'] === '无需处理'"
            title="确定要移除至未处置状态吗？"
            @confirm="handleRemoveToUndisposed(row)"
            confirm-button-text="确定"
            cancel-button-text="取消"
          >
            <template #reference>
              <el-button
                :size="size"
                :type="type"
                text
                :icon="useRenderIcon('EP-Remove')"
              >
                移除
              </el-button>
            </template>
          </el-popconfirm>
          <el-button
            :size="size"
            :type="type"
            text
            :icon="useRenderIcon('EP-View')"
            @click="detailViewHandler(row)"
          >
            详情
          </el-button>
        </template>
        <!-- 风险级别列 -->
        <template #vulLevel="{ row }">
          <el-tag
            :color="
              getRiskLevelColor02(
                row.vulLevel == '危急' ? '危急' : row.vulLevel
              )
            "
            class="text-white border-none"
          >
            {{
              getRiskLevelLabel02(
                row.vulLevel == "危急" ? "危急" : row.vulLevel
              )
            }}
          </el-tag>
        </template>
        <!-- 事件处理状态列 -->
        <template #dealStatus="{ row }">
          <el-tag
            effect="light"
            :type="getDealStatusType02(row.dealStatus + '')"
          >
            {{ getSegmentLabel02(row.dealStatus + "") }}
          </el-tag>
        </template>

        <template #vulsuggest="{ row }">
          <el-tooltip effect="dark" placement="top">
            <template #content>
              <div style="max-width: 666px">{{ row["vulsuggest"] }}</div>
            </template>
            <span
              style="
                width: 233px;
                white-space: nowrap;
                text-overflow: ellipsis;
                overflow: hidden;
                display: inline-block;
              "
            >
              {{ row["vulsuggest"] }}
            </span>
          </el-tooltip>
        </template>

        <template #dispatch_status="{ row }">
          <div>
            <el-tag
              v-if="row.dispatch_status == '已派单'"
              class="tag"
              size="small"
              type="success"
              >已派单</el-tag
            >
            <el-tag v-else class="tag" size="small" type="danger"
              >未派单</el-tag
            >
          </div>
        </template>
      </im-table>
    </div>

    <el-drawer
      title="漏洞详情"
      size="75%"
      destroy-on-close
      append-to-body
      v-model="showInfo"
    >
      <SecurityVulMonitorDetailDrawer_vulView
        v-if="showInfo"
        :vulRecord="currentInfoRow"
        :searchCondition="searchCondition"
      />
    </el-drawer>

    <el-drawer
      title="导入"
      size="45%"
      destroy-on-close
      append-to-body
      v-model="showImport"
    >
      <SecurityVulImportDrawer
        v-if="showImport"
        @refresh="queryEventData"
        :template-type="commandImportType"
      />
    </el-drawer>

    <el-dialog
      title="批量处置"
      width="33%"
      destroy-on-close
      append-to-body
      v-model="showHandle"
    >
      <vulnerabilityDetailsMoreOperations
        v-if="showHandle"
        @closeDraw="closeDraw"
        :batchOperationModelData="batchOperationRowsData"
      />
    </el-dialog>

    <EventDispatchModal
      @success="queryEventData"
      :form="eventDispatchModalForm"
      :modelValue="eventDispatchModalVisable"
      @update:modelValue="eventDispatchModalVisable = false"
    />
  </div>
</template>

<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  nextTick,
  onMounted,
  reactive,
  ref,
  toRefs,
  watch,
  h
} from "vue";
import dayjs from "dayjs";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElTree } from "element-plus";
// import SecurityVulMonitorDetailDrawer from "@/views/modules/eam/zcgl/instance/source/component/vulnerabilityDetails/SecurityVulMonitorDetailDrawer.vue";
import SecurityVulImportDrawer from "@/views/modules/security/securityVulnerability/components/SecurityVulImportDrawer.vue";
import SecurityVulMonitorDetailDrawer_vulView from "@/views/modules/security/securityVulnerability/components/SecurityVulMonitorDetailDrawer_vulView.vue";
import vulnerabilityDetailsMoreOperations from "@/views/modules/security/securityVulnerability/components/vulnerabilityDetailsMoreOperations_vulView.vue";
import EventDispatchModal from "@/views/modules/security/securityVulnerability/components/EventDispatchModal.vue";

import { defaultPageSize, pageSizeOptions } from "@/utils/page_util";
import {
  getDealStatusType,
  getRiskLevelColor,
  getRiskLevelLabel,
  getSegmentLabel,
  getDealStatusType02,
  getSegmentLabel02,
  riskLevelData,
  segmentData,
  getRiskLevelColor02,
  getRiskLevelLabel02,
  riskLevelData02
} from "@/views/modules/security/securityVulnerability/util/vulnerability_data";

import {
  vulDetailByAssetMethod,
  importVulDetailByAssetMethod,
  templateTypesMethod
} from "@/views/modules/security/securityVulnerability/api/vulnerabilityAssetTableInfoInterface_vulView";
import {
  bulkOrder,
  removeToUndisposedBatch,
  removeToUndisposed
} from "@/views/modules/security/securityVulnerability/api/vulnerabilityAssetTableInfoInterface";
import { Search } from "@element-plus/icons-vue";
import VulTaskEditor from "@/views/modules/security/securityVulnerability/components/batchVerification/VulTaskEditor.vue";
import { addDialog, closeAllDialog } from "@/components/ReDialog/index";
import ImTable from "@/components/ItsmCommon/table/ImTable.vue";
import {
  createColumnStorage,
  HeaderFilterValue,
  ImTableInstance
} from "@/components/ItsmCommon";
import {
  FilterOption,
  TableFilterDataProvider
} from "@/components/ItsmCommon/table/props";
import {
  queryVulTableHeadGroup,
  getQuerySyscodeList
} from "@/views/modules/security/securityVulnerability/api/vulnerabilityHomeInterface";

const { $router, $confirm, $message } =
  getCurrentInstance().appContext.config.globalProperties;

const deptTree = ref<InstanceType<typeof ElTree>>();
const appTreeRef = ref<InstanceType<typeof ElTree>>();
const tableRef = ref<ImTableInstance>();

const editTaskRef = ref<InstanceType<typeof VulTaskEditor>>();
const openEditTaskDialog = (t: any) => {
  addDialog({
    title: "漏洞探测新建任务",
    width: "80%",
    fullscreenIcon: true,
    closeOnClickModal: false,
    props: { taskInfo: t },
    contentRenderer: () =>
      h(VulTaskEditor, {
        ref: editTaskRef,
        onSuccess: () => {
          closeAllDialog();
          // loadTaskData();
        }
      }),
    beforeSure(done) {
      editTaskRef.value.submitData();
    }
  });
};

//数据对象
const state = reactive({
  checkAll: true,
  totalTagCount: 0,
  activeTabName: "deptView",
  tableLoading: false,
  deptKeyWord: null,
  // deptData: firstLevelDeptData,
  deptData: [],
  appKeyWord: null,
  appData: [],
  eventTagData: [],
  columnCondition: {
    value: null,
    field: "assetName",
    fuzzyable: true,
    operator: "fuzzy"
  },
  dateRangeSign: "30d",
  timeSegmentOptions: [
    {
      label: "全部",
      value: ""
    },
    {
      label: "近24小时",
      value: "1d"
    },
    {
      label: "近7天",
      value: "7d"
    },
    {
      label: "近30天",
      value: "30d"
    }
  ],
  dateTimeRange: [],
  searchCondition: {
    // orgId: "",
    // asset_app_name: "",
    dealWith: "1",
    syscode: [] as string[]
    // dealStatus: null,
    // vulLevel: "",
    // event_type_tag: null,
    // model: "event"
  },
  // "conditions": [
  //   { "field": "assetName", "fuzzyable": true, "operator": "fuzzy", "value": "name" },
  //   { "field": "ip", "fuzzyable": true, "operator": "fuzzy", "value": "ip" },
  //   { "field": "refOrgNameTree", "fuzzyable": false, "value": "zXCu-EN-Pn_2CISvf9V" },
  //   { "field": "asset_type_name", "fuzzyable": false, "value": 259 },
  //   { "field": "refsys", "fuzzyable": true, "operator": "fuzzy", "value": "system" },
  //   { "field": "vulLevel", "fuzzyable": false, "value": "4,3,2" },
  //   { "field": "dealStatus", "fuzzyable": false, "value": "1" }
  // ]
  columnSearchOptions: [
    {
      label: "资产名称",
      value: "assetName"
    },
    {
      label: "资产IP",
      value: "ip"
    },
    {
      label: "业务系统",
      value: "refsys"
    }
  ],
  tableData: [],
  // 资产信息(以ip为key)
  assetByIpMap: {},
  tablePage: {
    align: "right",
    total: 1,
    currentPage: 1,
    pageSize: defaultPageSize,
    pageSizes: pageSizeOptions
  },
  deal: {
    visible: false,
    title: "",
    unitIds: [],
    defaultAction: null
  },
  selectedEvents: [] as Array<string>,
  selectedEventRows: [],
  ipBlock: {
    visible: false,
    ipAddress: "",
    defaultPlugLabel: "安全事件"
  },
  dsSelData: [
    {
      value: 1,
      label: "未修复"
    },
    {
      value: 2,
      label: "已修复"
    }
  ],

  // 数据来源选项数据
  syscodeData: [],
  filters: []
});
const {
  activeTabName,
  tableLoading,
  deptKeyWord,
  deptData,
  appKeyWord,
  appData,
  eventTagData,
  columnCondition,
  dateTimeRange,
  searchCondition,
  columnSearchOptions,
  tableData,
  tablePage,
  deal,
  selectedEvents,
  ipBlock
} = toRefs(state);

const vulName = ref("");
const vulsuggestToolTipRef = ref(null);

const batchOperation = ref(["批量派单", "批量处置", "批量移除"]);

// 批量操作权限控制
const canBatchDispose = computed(() => {
  if (state.selectedEventRows.length === 0) return false;
  // 只有当勾选的项目全部为"未处置"状态时才能点击
  return state.selectedEventRows.every(row => row.dealStatus === "未修复");
});

const canBatchRemove = computed(() => {
  if (state.selectedEventRows.length === 0) return false;
  // 只有当勾选的项目全部为"无需处置"状态时才能点击
  return state.selectedEventRows.every(row => row.dealStatus === "无需处理");
});

// 控制处置按钮显示
const canShowDisposeButton = (row: any) => {
  // 当行状态为"无需处理"时，不显示处置按钮
  if (row.dealStatus === "无需处理") {
    return false;
  }
  // 当行状态为"已修复"时，不显示处置按钮
  if (row.dealStatus === "已修复") {
    return false;
  }
  // 只有"未修复"状态才显示处置按钮
  return row.dealStatus === "未修复";
};

watch(deptKeyWord, val => {
  deptTree.value!.filter(val);
});

watch(appKeyWord, val => {
  appTreeRef.value!.filter(val);
});

//定义事件
const emit = defineEmits([
  "jump-to",
  "event-select",
  "searchQuery",
  "getChangeTag"
]);

//根据页面高度设置表格高度
const tableHeight = computed(() => {
  return props.markTabChange
    ? document.documentElement.offsetHeight - 403
    : document.documentElement.offsetHeight - 300;
});

const tableKey = ref(0);
const props = defineProps({
  eventInfo: Object,
  markTabChange: Boolean,
  triggerVulViewTable: Object,
  heightTable: Number
});

const tableOption = reactive({
  align: "center",
  menuAlign: "center",
  border: true,
  stripe: true,
  selection: true,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 130,
  height: 0,
  rowKey: "vulId",
  column: []
});
watch(
  props,
  val => {
    console.log(val.heightTable);
    tableKey.value = tableKey.value + 1;
    tableKey.value = tableKey.value + 1;
    if (val.heightTable > 0) {
      tableOption.height = val.heightTable;
    }
  },
  { deep: true, immediate: true }
);

// console.log(props.eventInfo);

//重置分页后查询事件数据
const resetTablePageAndQuery = (
  toggleQueryCriteria = "",
  keepFiltersFlag?: boolean
) => {
  if (keepFiltersFlag !== true) {
    // 只要没有声明keepFilters = true，清除表格所有过滤器, 只有在on-filter中会指定keepFilters为true
    state.filters = [];
    tableRef.value.clearAllFilters();
  }

  state.tablePage.currentPage = 1;
  queryEventData(toggleQueryCriteria);
};

const tmpQuery = ref(null);
watch(props.triggerVulViewTable, val => {
  if (Object.keys(props.triggerVulViewTable.query).length == 0) {
    console.log("对象为空");
    tmpQuery.value = null;
  } else {
    console.log("对象不为空");
    tmpQuery.value = props.triggerVulViewTable.query;
  }

  // 检查是否是重置操作，如果是则清空筛选条件
  if (
    props.triggerVulViewTable["toggleQueryCriteria"] === "toggleQueryCriteria"
  ) {
    // 重置数据来源筛选器
    state.searchCondition["syscode"] = [];
    // 重置风险级别筛选器
    state.searchCondition["vulLevel"] = [];
    // 重置漏洞名称
    vulName.value = "";
    // 重置日期范围
    state.dateTimeRange = [];
    state.dateRangeSign = "30d";
    // 重置处理状态
    state.searchCondition["dealWith"] = "1";
  }

  resetTablePageAndQuery(props.triggerVulViewTable["toggleQueryCriteria"]);
});

/**
 * 日期范围(标签和列表能复用)
 */
const computedDateRange = computed(() => {
  let dateRange;
  if (state.dateRangeSign) {
    dateRange = state.dateRangeSign;
  } else {
    if (state.dateTimeRange?.length == 2) {
      dateRange = [
        dayjs(state.dateTimeRange[0]).format("YYYY-MM-DD HH:mm:ss"),
        dayjs(state.dateTimeRange[1]).endOf("day").format("YYYY-MM-DD HH:mm:ss")
      ];
    } else {
      dateRange = null;
    }
  }
  return dateRange;
});

const searchTmpData = tmpConditions => {
  // if (state.searchCondition['vulLevel'] && state.searchCondition['vulLevel'].length > 0) {
  //     tmpConditions.push({
  //         "field": "vulLevel",
  //         "fuzzyable": false,
  //         "value": state.searchCondition['vulLevel'].join(',')
  //     })
  // }
  if (
    state.searchCondition["dealStatus"] &&
    state.searchCondition["dealStatus"].length > 0
  ) {
    tmpConditions.push({
      field: "dealStatus",
      fuzzyable: false,
      value: state.searchCondition["dealStatus"].join(",")
    });
  }
  if (state.searchCondition["deptName"]) {
    tmpConditions.push({
      field: "refOrgNameTree",
      fuzzyable: false,
      value: state.searchCondition["deptName"]
    });
  }
  if (state.searchCondition["asset_type_name"]) {
    tmpConditions.push({
      field: "asset_type_name",
      fuzzyable: false,
      value: state.searchCondition["asset_type_name"]
    });
  }
  if (props.eventInfo && props.eventInfo["ip"]) {
    tmpConditions.push({
      field: "ip",
      fuzzyable: true,
      operator: "exact",
      value: props.eventInfo["ip"]
    });
  }
  if (vulName.value && vulName.value.length > 0) {
    tmpConditions.push({
      field: "vulName",
      fuzzyable: true,
      operator: "fuzzy",
      value: vulName.value
    });
  }

  // 数据来源筛选条件
  if (
    state.searchCondition["syscode"] &&
    state.searchCondition["syscode"].length > 0
  ) {
    tmpConditions.push({
      field: "syscode",
      fuzzyable: false,
      value: state.searchCondition["syscode"].join(",")
    });
  }
};

const buildQueryCondition = () => {
  const tmpConditions = [];
  searchTmpData(tmpConditions);
  return {
    //查询条件
    conditions: state.columnCondition.value
      ? tmpQuery.value
        ? [state.columnCondition, ...tmpConditions, tmpQuery.value]
        : [state.columnCondition, ...tmpConditions]
      : tmpQuery.value
        ? [...tmpConditions, tmpQuery.value]
        : [...tmpConditions],
    //日期范围
    dateRange: computedDateRange.value,
    // dateRange: 'all',
    //搜索条件
    // dealWith: +state.searchCondition['dealWith'],
    dealStatus:
      +state.searchCondition["dealWith"] == 0
        ? ""
        : +state.searchCondition["dealWith"] == 1
          ? "undisposed"
          : +state.searchCondition["dealWith"] == 2
            ? "disposalof"
            : "noNeedHandle",
    // ip: props.eventInfo['ip'],
    // model: "event",
    //当前页码
    pageNum: state.tablePage.currentPage,
    //每页显示条数
    pageSize: state.tablePage.pageSize,
    orgId: props.triggerVulViewTable.orgId,
    assetApp: props.triggerVulViewTable["assetApp"],
    vulLevel:
      state.searchCondition["vulLevel"] &&
      state.searchCondition["vulLevel"].join(","),
    vulType:
      props.triggerVulViewTable["vulType"] &&
      props.triggerVulViewTable["vulType"],

    // 数据来源
    syscode:
      state.searchCondition["syscode"] &&
      state.searchCondition["syscode"].length > 0
        ? state.searchCondition["syscode"].join(",")
        : "",

    // 视角标识
    viewType: "vul",
    // 列头过滤器
    headerFilter: {
      filters: state.filters
    }
  };
};

//查询事件数据
const queryEventData = async (toggleQueryCriteria = "") => {
  state.selectedEventRows = [];
  //设置表格加载状态为true
  state.tableLoading = true;
  state.tableData = [];
  dealFuzzEnable();

  try {
    const tmpConditions = [];

    searchTmpData(tmpConditions);

    //根据条件查询事件列表
    const res = await vulDetailByAssetMethod(buildQueryCondition());
    //设置表格数据
    state.tableData = res["data"].rows;

    // 处理动态表头
    tableOption.column.length = 0;
    res["data"]["columns"].forEach(item => {
      if (res["data"]["showList"].includes(item["field"])) {
        switch (item["field"]) {
          case "undisposed":
            item["name"] = "未修复资产";
            break;
          case "disposalof":
            item["name"] = "已修复资产";
            break;
          case "noNeedHandle":
            item["name"] = "无需处理资产";
            break;
          default:
            break;
        }
        tableOption.column.push({
          hide: item["field"] == "notHandledYet",
          prop: item["field"],
          label: item["name"],
          fieldType: item["fieldType"],
          width:
            item["name"] == "最新发现时间" ||
            item["name"] == "首次发现时间" ||
            item["name"] == "解决方案" ||
            item["name"] == "漏洞名称" ||
            item["name"] == "资产名称"
              ? item["name"] == "解决方案"
                ? 300
                : 200
              : "",
          unit: item["unit"],
          component: item["component"],
          filters: true,
          sortable: true
        });
      }
    });

    // 处理查询项
    const tmpQuerySearch = [];
    res["data"]["columns"].forEach(item => {
      const obj = { ...item["component"] };
      if (!("disableSearch" in obj)) {
        // if (item['component']['type'] == 'Input') {
        tmpQuerySearch.push({
          value: item["field"],
          label: item["name"],
          type: item["field"] == "vulLevel" ? "Select" : "Input"
        });
        // }
      }
    });
    if (toggleQueryCriteria != "toggleQueryCriteria") {
      console.log('emit("searchQuery", tmpQuerySearch);', toggleQueryCriteria);
      emit("searchQuery", tmpQuerySearch);
    }
    emit("getChangeTag");

    //设置表格总条数
    state.tablePage.total = res["data"].totalElements;
  } catch (e) {
    console.error(e);
  }
  //设置表格加载状态为false
  state.tableLoading = false;
};

// 日期清空后默认30d
const dateChangeFunction = query => {
  state.dateRangeSign = null;
  if (!query) {
    state.dateRangeSign = "30d";
  }
  resetTablePageAndQuery();
};

//处理模糊标识
function dealFuzzEnable() {
  if (state.columnCondition.fuzzyable) {
    state.columnCondition.operator = "fuzzy";
  } else {
    state.columnCondition.operator = "exact";
  }
}

const segmentChangeHandler = () => resetTablePageAndQuery();

//风险级别改变触发
const riskLevelChangeHandler = (level: string) => {
  resetTablePageAndQuery();
};

//数据来源改变触发
const syscodeChangeHandler = (selectedValues: string[]) => {
  console.log("数据来源选择变化:", selectedValues);
  resetTablePageAndQuery();
};

const showInfo = ref(false);
const currentInfoRow = ref(null);
//查看事件详情触发
const detailViewHandler = (evt: any) => {
  // emit("event-select", evt);
  // jumpTo("eventDetailInfo");
  showInfo.value = true;
  currentInfoRow.value = evt;
};

//表格选择改变触发
const selectionChangeHandler = (selRows: Array<any>) => {
  const selectIds = [];
  selRows.forEach(row => selectIds.push(row.vulId));
  state.selectedEvents = selectIds;
  console.log(selectIds.join());
  state.selectedEventRows = selRows;
};

//导出事件数据触发
const exportEventHandler = () => {
  $confirm(`您确认要导出当前查询条件下的数据么？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消"
  }).then(async () => {
    $message({
      message: "数据正在导出中...",
      type: "success"
    });
    const tmpConditions = [];
    searchTmpData(tmpConditions);
    await importVulDetailByAssetMethod({
      //查询条件
      conditions: state.columnCondition.value
        ? [state.columnCondition, ...tmpConditions]
        : [...tmpConditions],
      //日期范围
      dateRange: computedDateRange.value,
      dealStatus:
        +state.searchCondition["dealWith"] == 0
          ? ""
          : +state.searchCondition["dealWith"] == 1
            ? "undisposed"
            : +state.searchCondition["dealWith"] == 2
              ? "disposalof"
              : "noNeedHandle",
      //搜索条件
      // dealWith: +state.searchCondition['dealWith'],
      // ip: props.eventInfo['ip'],
      // model: "event",
      //当前页码
      pageNum: state.tablePage.currentPage,
      //每页显示条数
      pageSize: state.tablePage.pageSize,
      orgId: props.triggerVulViewTable.orgId,
      assetApp: props.triggerVulViewTable["assetApp"],
      vulLevel:
        state.searchCondition["vulLevel"] &&
        state.searchCondition["vulLevel"].join(","),
      vulType:
        props.triggerVulViewTable["vulType"] &&
        props.triggerVulViewTable["vulType"],
      // 数据来源
      syscode:
        state.searchCondition["syscode"] &&
        state.searchCondition["syscode"].length > 0
          ? state.searchCondition["syscode"].join(",")
          : ""
    });
  });
};
const showImport = ref(false);
const commandImportType = ref("");

const showHandle = ref(false);
const batchOperationRowsData = ref([]);
const eventDispatchModalVisable = ref(false);
const eventDispatchModalForm = reactive({
  title: "",
  deptId: "",
  userId: [],
  params: ""
});
const initEventDispatchModalForm = () => {
  eventDispatchModalForm.title = "";
  eventDispatchModalForm.deptId = "";
  eventDispatchModalForm.userId = [];
  eventDispatchModalForm.params = "";
};
const handleCommand = (command, info = "", onlyOne = "") => {
  initEventDispatchModalForm();
  console.log(command, state.selectedEventRows);
  if (info == "moreFunction") {
    if (onlyOne == "one") {
      const tmpVulArray = state.selectedEventRows.map(item => {
        return {
          businessId: item["vulId"]
          // capitalId: item['ip'],
        };
      });
      batchOperationRowsData.value = {
        tmpVulArray,
        ...command,
        onlyOne
      };
      showHandle.value = true;
      return;
    } else {
      batchOperationRowsData.value = [...state.selectedEventRows];
      showHandle.value = true;
      return;
    }
  } else if (info == "批量派单") {
    eventDispatchModalVisable.value = true;
    const tmpVulArray = state.selectedEventRows.map(item => {
      return item["vulId"];
    });
    // console.log(tmpVulArray.join());
    eventDispatchModalForm.params = tmpVulArray.join();
    // console.log(JSON.stringify(tmpVulArray));
    // try {
    //     bulkOrder({
    //         "params": JSON.stringify(tmpVulArray),
    //         "type": "bug",
    //         "multiTab": false
    //     }).then(res => {
    //         console.log(res);
    //         $message({
    //             message: res['msg'],
    //             type: "info"
    //         });
    //     }).catch(err => {
    //         $message({
    //             message: err,
    //             type: "info"
    //         });
    //     })
    // } catch (error) {
    //     $message({
    //         message: error,
    //         type: "info"
    //     });
    // }

    return;
  } else if (info == "批量移除") {
    // 过滤出处置状态为"无需处理"的数据
    const noNeedHandleRows = state.selectedEventRows.filter(
      item => item["dealStatus"] === "无需处理"
    );
    const totalSelectedRows = state.selectedEventRows.length;
    const filteredOutCount = totalSelectedRows - noNeedHandleRows.length;

    if (noNeedHandleRows.length === 0) {
      $message({
        message: '当前选中的数据中没有处置状态为"无需处理"的数据',
        type: "warning"
      });
      return;
    }

    // 构建确认消息
    let confirmMessage = `您确认要将 ${noNeedHandleRows.length} 条处置状态为\"无需处理\"的数据移除到未处置吗？`;

    // 如果有数据被过滤掉，添加提示信息
    if (filteredOutCount > 0) {
      confirmMessage += `\n\n注意：您选中了 ${totalSelectedRows} 条数据，其中 ${filteredOutCount} 条非\"无需处理\"状态的数据将被过滤，不会执行操作。`;
    }

    // 确认对话框
    $confirm(confirmMessage, "批量移除确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
      dangerouslyUseHTMLString: false
    })
      .then(async () => {
        try {
          // 提取 vulId 并用逗号拼接
          const vulIds = noNeedHandleRows.map(item => item["vulId"]).join(",");

          // 调用API
          const res = await removeToUndisposedBatch({
            vulIds: vulIds
          });

          $message({
            message: "批量移除操作成功",
            type: "success"
          });

          // 刷新表格数据
          queryEventData();
        } catch (error) {
          $message({
            message: "批量移除操作失败",
            type: "error"
          });
        }
      })
      .catch(() => {
        // 用户取消操作
      });
    return;
  }
  showImport.value = true;
  commandImportType.value = command;
};

const closeDraw = () => {
  queryEventData();
  showHandle.value = false;
};

// 处理单个移除操作
const handleRemoveToUndisposed = async (row: any) => {
  try {
    // 调用单个移除API
    const res = await removeToUndisposed({
      vulId: row.vulId
    });

    // 显示成功提示
    $message({
      message: "移除至未处置状态成功",
      type: "success"
    });

    // 刷新表格数据
    queryEventData();
  } catch (error) {
    // 显示失败提示
    $message({
      message: "移除操作失败",
      type: "error"
    });
  }
};

// 导入种类
const importTemplate = ref([]);
const templateTypesAxios = async () => {
  const res = await templateTypesMethod();
  importTemplate.value = res["data"];
};

// 数据来源
const loadSyscodeData = async () => {
  try {
    const res = (await getQuerySyscodeList()) as any;
    if (res.status === "0" && res.data && Array.isArray(res.data)) {
      state.syscodeData = res.data;
    }
  } catch (error) {
    console.error("加载数据来源失败:", error);
  }
};

onMounted(() => {
  queryEventData();
  templateTypesAxios();
  loadSyscodeData();
});

const filterDataProvider: TableFilterDataProvider = {
  options: (prop, filters: HeaderFilterValue[]) => {
    return new Promise<{ total: number; options: FilterOption[] }>(resolve => {
      const query = buildQueryCondition() as any;
      // 覆盖headerFilter
      query["headerFilter"] = {
        prop,
        filters
      };
      queryVulTableHeadGroup(query)
        .then(res => {
          console.log(res);
          resolve(res.data);
        })
        .catch(err => {
          console.error(err);
          resolve({ total: 0, options: [] });
        });
    });
  },
  onFilter: (filters: HeaderFilterValue[]) => {
    state.filters = filters;
    resetTablePageAndQuery("", true);
  }
};

//跳转
const jumpTo = (sign: string) => {
  emit("jump-to", sign);
};
</script>

<style lang="scss" scoped>
:deep(.el-popper) {
  max-width: 666px !important;
}
</style>
